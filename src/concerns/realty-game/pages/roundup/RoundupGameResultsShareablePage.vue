<template>
  <div class="realty-game-results-shareable">
    <div class="container q-pa-md">
      <!-- Loading State -->
      <div v-if="isLoading"
           class="loading-container text-center q-pa-xl">
        <q-spinner-dots color="primary"
                        size="3em" />
        <div class="text-h6 q-mt-md">Loading results...</div>
      </div>

      <!-- Error State -->
      <div v-else-if="error"
           class="error-container text-center q-pa-xl">
        <q-icon name="error"
                color="negative"
                size="3em" />
        <div class="text-h6 q-mt-md text-negative">{{ error }}</div>
        <q-btn color="primary"
               label="Try Again"
               @click="loadResults"
               class="q-mt-md" />
      </div>

      <!-- Results Content -->
      <div v-else-if="results"
           class="results-content">
        <!-- Results Header -->
        <div class="results-header q-mb-xl text-center">
          <h1 class="text-h4 text-weight-bold text-primary q-mb-lg">
            {{ ssGameSession.game_player_nickname }}'s Property Price Challenge Results
          </h1>

          <div class="text-h6 text-grey-7 q-mb-lg">
            {{ ssGameSession.game_title || 'Property Price Challenge' }}
          </div>

          <!-- Performance Badge -->
          <div class="performance-section q-mb-lg">
            <RealtyGamePerformanceBadge
              :player-results="playerResults"
            />
          </div>
        </div>

        <!-- Share Info Card -->
        <q-card class="share-info-card q-mb-xl"
                flat
                bordered>
          <q-card-section class="q-pa-lg text-center">
            <q-icon name="share"
                    color="primary"
                    size="2em"
                    class="q-mb-md" />
            <div class="text-h6 text-weight-medium q-mb-sm">
              Share Your Results
            </div>
            <div class="text-body1 text-grey-7 q-mb-lg">
              Show your friends how well you know property prices!
            </div>
            <q-btn color="primary"
                   icon="share"
                   label="Share Results"
                   @click="shareResults"
                   size="lg"
                   class="q-px-xl" />
          </q-card-section>
        </q-card>

        <!-- Full Results Card -->
        <q-card class="full-results-card q-mb-xl"
                flat
                bordered>
          <q-card-section class="q-pa-lg text-center">
            <q-icon name="analytics"
                    color="info"
                    size="2em"
                    class="q-mb-md" />
            <div class="text-h6 text-weight-medium q-mb-sm">
              Want to See Full Results?
            </div>
            <div class="text-body1 text-grey-7 q-mb-lg">
              View detailed breakdown with all property prices and analysis
            </div>
            <q-btn color="info"
                   icon="visibility"
                   label="View Detailed Results"
                   @click="goToDetailedPage"
                   size="lg"
                   class="q-px-xl" />
          </q-card-section>
        </q-card>

        <!-- Social Sharing Component -->
        <div class="social-sharing-section q-mb-xl">
          <SocialSharing
            :socialSharingPrompt="`Check out ${ssGameSession.game_player_nickname}'s Property Price Challenge results!`"
            :socialSharingTitle="`${ssGameSession.game_player_nickname}'s Property Price Challenge Results`"
            :urlProp="shareableResultsUrl"
          />
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons text-center q-mb-xl">
          <q-btn color="primary"
                 icon="play_arrow"
                 label="Start New Game"
                 @click="startNewGame"
                 size="lg"
                 class="q-mr-md q-mb-sm" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useQuasar } from 'quasar'
import RealtyGamePerformanceBadge from '../../components/RealtyGamePerformanceBadge.vue'
import SocialSharing from 'src/concerns/dossiers/components/sharing/SocialSharing.vue'

// Props will be provided by the parent layout
const props = defineProps({
  gameSessionId: {
    type: String,
    required: true,
  },
  gameCommunitiesDetails: {
    type: Object,
  },
  shareableResultsUrl: {
    type: String,
    required: true,
  },
  isCurrentUserSession: {
    type: Boolean,
    required: true,
  },
  // Data from parent layout
  isLoading: {
    type: Boolean,
    default: false,
  },
  error: {
    type: [String, null],
    default: null,
  },
  results: {
    type: Object,
    default: null,
  },
  playerResults: {
    type: Object,
    default: () => ({}),
  },
  ssGameSession: {
    type: Object,
    default: () => ({}),
  },
  comparisonSummary: {
    type: Array,
    default: () => [],
  },
  gameBreakdown: {
    type: Array,
    default: () => [],
  },
  getScoreColor: {
    type: Function,
    required: true,
  },
})

// Emit events to parent layout
const emit = defineEmits(['load-results'])

const $router = useRouter()
const $route = useRoute()
const $q = useQuasar()

// Methods
const loadResults = () => {
  emit('load-results')
}

const shareResults = () => {
  const url = window.location.href
  const text = `Check out ${props.ssGameSession.game_player_nickname}'s Property Price Challenge results! Score: ${props.playerResults.total_score}/${props.playerResults.max_possible_score} (${props.playerResults.performance_rating?.rating})`

  if (navigator.share) {
    navigator.share({
      title: `${props.ssGameSession.game_player_nickname}'s Property Price Challenge Results`,
      text: text,
      url: url
    }).catch(() => {
      fallbackShare(url, text)
    })
  } else {
    fallbackShare(url, text)
  }
}

const fallbackShare = (url, text) => {
  navigator.clipboard.writeText(`${text}\n${url}`).then(() => {
    $q.notify({
      type: 'positive',
      message: 'Results link copied to clipboard!',
      position: 'top'
    })
  }).catch(() => {
    $q.notify({
      type: 'info',
      message: 'Share this link: ' + url,
      position: 'top',
      timeout: 5000
    })
  })
}

const goToDetailedPage = () => {
  $router.push({
    name: 'rRoundupGameResultsDetailed',
    params: {
      gameSlug: $route.params.gameSlug,
      listingInGameUuid: $route.params.listingInGameUuid,
      gameSessionId: props.gameSessionId
    }
  })
}

const startNewGame = () => {
  $router.push({
    name: 'rRoundupGameStart',
    params: { gameSlug: $route.params.gameSlug }
  })
}

// Lifecycle
onMounted(() => {
  if (!props.results) {
    loadResults()
  }
})
</script>

<style scoped>
.container {
  max-width: 1000px;
  margin: 0 auto;
}

.share-info-card,
.full-results-card {
  border-radius: 12px;
}

.performance-section {
  display: flex;
  justify-content: center;
  align-items: center;
}

.action-buttons {
  margin-top: 2rem;
}

@media (max-width: 768px) {
  .action-buttons .q-btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }
}
</style>
