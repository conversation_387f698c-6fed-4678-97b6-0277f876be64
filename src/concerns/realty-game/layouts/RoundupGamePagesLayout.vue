<template>
  <div class="roundup-game-pages-layout">
    <div class="row items-center justify-between q-pa-lg">
      <div v-if="$route.name !== 'rRoundupGameProperty'" class="text-center full-width">
        <div>
          <h1 class="text-h4 text-weight-bold text-primary q-mb-sm">
            <router-link
              :to="{ name: 'rRoundupGameProperty', params: {} }"
              class=""
            >
              {{ realtyGameListingDetails?.game_listing_display_title || 'Property Price Challenge' }}
            </router-link>
          </h1>
        </div>
      </div>
    </div>
    <router-view
      :game-session-id="gameSessionId"
      :listing-in-game-uuid="listingInGameUuid"
      :game-communities-details="gameCommunitiesDetails"
      :shareable-results-url="shareableResultsUrl"
      :is-current-user-session="isCurrentUserSession"
      :game-title="gameTitle"
      :game-default-currency="gameDefaultCurrency"
      :total-properties="totalProperties"
      :first-prop-listing="firstPropListing"
      :realty-game-summary="realtyGameSummary"
      :is-loading="isLoading"
      :error="error"
      :results="results"
      :player-results="playerResults"
      :ss-game-session="ssGameSession"
      :comparison-summary="comparisonSummary"
      :game-breakdown="gameBreakdown"
      :overall-ranking="overallRanking"
      :leaderboard="leaderboard"
      :show-leaderboard="showLeaderboard"
      :get-score-color="getScoreColor"
      :format-price-with-both-currencies="formatPriceWithBothCurrencies"
      :current-property-data="currentPropertyData"
      :is-property-loading="isPropertyLoading"
      :property-error="propertyError"
      @load-results="handleLoadResults"
      @update-progress="handleProgressUpdate"
      @game-complete="handleGameComplete"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'
import { useSingleListingRealtyGame } from 'src/concerns/realty-game/composables/useSingleListingRealtyGame'
import { useRealtyGameStorage } from 'src/concerns/realty-game/composables/useRealtyGameStorage'
import { useServerSingleListingGameResults } from 'src/concerns/realty-game/composables/useServerSingleListingGameResults'
import { useCurrencyConverter } from 'src/concerns/realty-game/composables/useCurrencyConverter'
import { useRealtyGameStore } from 'src/stores/realtyGame'
// import { useRealtyGameMetaStore } from 'src/stores/realtyGameMeta'

const $route = useRoute()
const $router = useRouter()
const $q = useQuasar()

// Initialize composables
const {
  totalProperties,
  gameTitle,
  realtyGameSummary,
  gameCommunitiesDetails,
  firstPropListing,
  fetchPriceGuessData,
  gameDefaultCurrency,
  setRealtyGameData,
} = useSingleListingRealtyGame()

const { getCurrentSessionId, getCurrencySelection } = useRealtyGameStorage()

// Initialize stores
const realtyGameStore = useRealtyGameStore()

const {
  isLoading,
  error,
  results,
  playerResults,
  comparisonSummary,
  gameBreakdown,
  ssGameSession,
  fetchResults,
  getScoreColor,
  realtyGameListingDetails,
} = useServerSingleListingGameResults()

const { setCurrency, formatPriceWithBothCurrencies } = useCurrencyConverter()

// Additional state for property data
const currentPropertyData = ref(null)
const isPropertyLoading = ref(false)
const propertyError = ref(null)

// Computed properties
const gameSessionId = computed(
  () => $route.query.session || $route.params.gameSessionId || ''
)

const listingInGameUuid = computed(() => $route.params.listingInGameUuid || '')

const shareableResultsUrl = computed(() => {
  if (!gameSessionId.value || !$route.params.gameSlug) {
    return ''
  }
  let shareRoute = {
    name: 'rRoundupGameResultsShareable',
    params: {
      gameSlug: $route.params.gameSlug,
      listingInGameUuid: listingInGameUuid.value,
      gameSessionId: gameSessionId.value,
    },
  }
  let fullPath = `${location.origin}${$router.resolve(shareRoute).href}`
  return fullPath
})

const isCurrentUserSession = computed(() => {
  const currentSessionId = getCurrentSessionId()
  return currentSessionId === gameSessionId.value
})

// Results-specific computed properties
const overallRanking = computed(() => results.value?.overall_ranking || null)
const leaderboard = computed(() => results.value?.leaderboard || [])
const showLeaderboard = computed(() => {
  return leaderboard.value && leaderboard.value.length > 1
})

// Event handlers
const handleLoadResults = async () => {
  if (gameSessionId.value && $route.params.listingInGameUuid) {
    await fetchResults(gameSessionId.value, $route.params.listingInGameUuid)
  }
}

const handleProgressUpdate = (data) => {
  // Handle progress updates from child components
  console.log('Progress update:', data)
}

const handleGameComplete = (sessionId) => {
  // Navigate to results page
  $router.push({
    name: 'rRoundupGameResultsSummary',
    params: {
      gameSlug: $route.params.gameSlug,
      listingInGameUuid: listingInGameUuid.value,
      gameSessionId: sessionId,
    },
  })
}

// Initialize currency from session
const initializeCurrency = () => {
  const sessionCurrency = getCurrencySelection(gameSessionId.value)
  if (sessionCurrency) {
    setCurrency(sessionCurrency)
  }
}

// Fetch property data
const fetchPropertyData = async (listingInGameUuid) => {
  if (!listingInGameUuid) return

  isPropertyLoading.value = true
  propertyError.value = null

  try {
    const response = await axios.get(
      `${pwbFlexConfig.dataApiBase}/api_public/v4/game_sale_listings/show_rgl/${listingInGameUuid}`
    )
    currentPropertyData.value = response.data.sale_listing
  } catch (err) {
    console.error('Failed to load property data:', err)
    propertyError.value = 'Failed to load property data'
    $q.notify({
      color: 'negative',
      message: 'Failed to load property data',
      icon: 'error',
    })
  } finally {
    isPropertyLoading.value = false
  }
}

// Initialize game data
const initializeGame = async () => {
  try {
    if ($route.params.gameSlug) {
      await fetchPriceGuessData($route.params.gameSlug)
    }
  } catch (err) {
    console.error('Failed to load game data:', err)
    $q.notify({
      color: 'negative',
      message: 'Failed to load game data',
      icon: 'error',
    })
  }
}

// Lifecycle
onMounted(async () => {
  // Check if we have preFetched data in the store
  if (realtyGameStore.isDataLoaded && realtyGameStore.gameListings.length > 0) {
    console.log('Using preFetched data from store')
    // Sync store data with composable
    setRealtyGameData({
      properties: realtyGameStore.gameListings,
      gameTitle: realtyGameStore.gameTitle,
      gameDesc: realtyGameStore.gameDesc,
      gameBgImageUrl: realtyGameStore.gameBgImageUrl,
      gameDefaultCurrency: realtyGameStore.gameDefaultCurrency,
    })

    // Set property data if available
    if (realtyGameStore.currentProperty) {
      currentPropertyData.value = realtyGameStore.currentProperty
    }
  } else {
    // Fallback: fetch data if not preFetched
    await initializeGame()
  }

  // Fetch property data if we're on a property page and don't have it
  if (listingInGameUuid.value && !currentPropertyData.value) {
    await fetchPropertyData(listingInGameUuid.value)
  }

  initializeCurrency()

  // Load results if we're on a results page
  const resultsRoutes = [
    'rRoundupGameResultsSummary',
    'rRoundupGameResultsShareable',
    'rRoundupGameResultsDetailed',
  ]

  if (resultsRoutes.includes($route.name) && gameSessionId.value) {
    await handleLoadResults()
  }
})

// Watch for route changes to reload results and fetch property data
watch(
  () => [
    $route.name,
    gameSessionId.value,
    $route.params.gameSlug,
    listingInGameUuid.value,
  ],
  async ([routeName, sessionId, gameSlug, newListingInGameUuid]) => {
    const resultsRoutes = [
      'rRoundupGameResultsSummary',
      'rRoundupGameResultsShareable',
      'rRoundupGameResultsDetailed',
    ]

    if (resultsRoutes.includes(routeName) && sessionId && gameSlug) {
      await handleLoadResults()
    }

    // Fetch property data if listingInGameUuid changed and we're on a property page
    if (newListingInGameUuid && routeName === 'rRoundupGameProperty') {
      await fetchPropertyData(newListingInGameUuid)
    }
  }
)
</script>

<script>
// preFetch hook for SSR and data prefetching
export async function preFetch({ currentRoute, ssrContext }) {
  console.log(
    'RoundupGamePagesLayout preFetch called! Route:',
    currentRoute.path,
    'Params:',
    currentRoute.params
  )

  const gameSlug = currentRoute.params.gameSlug
  const listingInGameUuid = currentRoute.params.listingInGameUuid

  if (!gameSlug) {
    console.warn('Missing gameSlug in preFetch')
    return
  }

  const { default: axios } = await import('axios')
  const { pwbFlexConfig } = await import('boot/pwb-flex-conf')
  const { useRealtyGameStore } = await import('src/stores/realtyGame')
  // const { useRealtyGameMetaStore } = await import('src/stores/realtyGameMeta')

  try {
    const requests = [
      axios.get(
        `${pwbFlexConfig.dataApiBase}/api_public/v4/realty_game_summary/${gameSlug}`
      ),
    ]

    // Add property request if we have listingInGameUuid (for property page)
    if (listingInGameUuid) {
      requests.push(
        axios.get(
          `${pwbFlexConfig.dataApiBase}/api_public/v4/game_sale_listings/show_rgl/${listingInGameUuid}`
        )
      )
    }

    const responses = await Promise.all(requests)
    const gameResponse = responses[0]
    const propertyResponse = responses[1] // undefined if no property request

    // Store data in Pinia store for SSR persistence
    if (gameResponse.data) {
      const gameData = gameResponse.data.price_guess_inputs
      const realtyGameDetails = gameResponse.data.realty_game_details
      const gameListings =
        gameData?.game_listings?.filter(
          (game) => game.listing_details.visible === true
        ) || []

      // Update Pinia store state
      const store = useRealtyGameStore()

      const storeData = {
        gameListings: gameListings.map((game) => game.listing_details),
        gameTitle: realtyGameDetails?.game_title || 'Property Price Challenge',
        gameDesc: realtyGameDetails?.game_description || '',
        gameBgImageUrl: realtyGameDetails?.game_bg_image_url || '',
        gameDefaultCurrency: gameData?.default_currency || 'GBP',
        totalProperties: gameListings.length,
        isDataLoaded: true,
      }

      // Add property data if available
      if (propertyResponse?.data) {
        storeData.currentProperty = propertyResponse.data.sale_listing
        storeData.listingInGameUuid = listingInGameUuid
      }

      store.setRealtyGameData(storeData)

      return {
        gameData: gameResponse.data,
        propertyData: propertyResponse?.data || null,
      }
    }
  } catch (error) {
    console.error('RoundupGamePagesLayout preFetch error:', error)
    return null
  }
}

export default {
  name: 'RoundupGamePagesLayout',
  preFetch,
}
</script>

<style scoped>
.roundup-game-pages-layout {
  min-height: 100vh;
}
</style>
